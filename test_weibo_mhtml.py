#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微博MHTML保存功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from store.weibo.weibo_store_mhtml import WeiboMhtmlStoreImplement
from tools import utils


async def test_weibo_mhtml_functionality():
    """测试微博MHTML保存功能"""
    
    print("=" * 60)
    print("微博MHTML保存功能测试")
    print("=" * 60)
    
    # 检查配置
    print(f"📋 当前配置:")
    print(f"   SAVE_DATA_OPTION: {config.SAVE_DATA_OPTION}")
    print(f"   ENABLE_GET_MEDIAS: {config.ENABLE_GET_MEDIAS}")
    
    # 创建MHTML存储实例
    mhtml_store = WeiboMhtmlStoreImplement()
    
    # 模拟微博数据（基于实际的微博数据结构）
    test_weibo_data = {
        "note_id": "5203546119084130",
        "nickname": "止谣君",
        "user_id": "1837234894",
        "content": """【衡阳之战的几点问题】

1、曾任国府国防部作战厅长的郭汝瑰将军回忆录：

"原暂五师的柏松林参谋，1944年在方先觉部任情报科长。柏告诉我，投降是方先觉叫副官处长出去联络的，投降时部队撤下来还有一万多人，机枪迫击炮等尚多，方先觉一见就哭起来了。对柏说：'早晓得还有这么多枪和人，我就不投降了。'"

2、第十军军长方先觉率四个师长向日军集体投降。方先觉投降后，仍有官兵在继续抵抗日军。

3、军令部长徐永昌上将1944年8月15日日记："据逃出之梁团长子超（第10军190师569团长）在连络站电话，围攻衡阳之敌于七日突破三个缺口窜入城内，方军长派其副官处长向敌提出六项要求与敌接洽。"

这是一个包含历史资料和图片的微博内容示例。""",
        "created_at": "Mon Aug 25 15:05:35 +0800 2025",
        "attitudes_count": 1234,
        "comments_count": 567,
        "reposts_count": 89,
        "local_image_paths": [
            "data/weibo/images/6d81facegy1hq0uz3fiddj20u00yhtd2.jpg",
            "data/weibo/images/6d81facegy1hq0vcrgsb2j20jc04ijs2.jpg",
            "data/weibo/images/6d81facegy1hq0vl5gj12j20qo0esn04.jpg"
        ],
        "image_urls": [
            "https://wx1.sinaimg.cn/large/6d81facegy1hq0uz3fiddj20u00yhtd2.jpg",
            "https://wx4.sinaimg.cn/large/6d81facegy1hq0vcrgsb2j20jc04ijs2.jpg",
            "https://wx1.sinaimg.cn/large/6d81facegy1hq0vl5gj12j20qo0esn04.jpg"
        ]
    }
    
    print(f"\n🔍 测试数据分析:")
    print(f"   微博ID: {test_weibo_data['note_id']}")
    print(f"   用户: {test_weibo_data['nickname']} ({test_weibo_data['user_id']})")
    print(f"   内容长度: {len(test_weibo_data['content'])} 字符")
    print(f"   图片数量: {len(test_weibo_data['local_image_paths'])}")
    
    # 检查图片文件是否存在
    print(f"\n📸 图片文件检查:")
    existing_images = []
    for i, image_path in enumerate(test_weibo_data['local_image_paths'], 1):
        if os.path.exists(image_path):
            file_size = os.path.getsize(image_path)
            print(f"   ✅ 图片{i}: {os.path.basename(image_path)} ({file_size} bytes)")
            existing_images.append(image_path)
        else:
            print(f"   ❌ 图片{i}: {os.path.basename(image_path)} (不存在)")
    
    # 更新测试数据中的图片路径（只包含存在的图片）
    test_weibo_data['local_image_paths'] = existing_images
    
    print(f"\n🧪 开始MHTML生成测试:")
    
    try:
        # 测试文件名生成
        save_file_name = mhtml_store.make_save_file_name(test_weibo_data)
        print(f"   ✅ 生成文件路径: {save_file_name}")
        
        # 测试MHTML内容生成
        print(f"   🔄 生成MHTML内容...")
        mhtml_content = await mhtml_store.generate_mhtml_content(test_weibo_data)
        
        print(f"   ✅ MHTML内容生成成功")
        print(f"   📊 内容统计:")
        print(f"      总长度: {len(mhtml_content)} 字符")
        print(f"      包含边界标记: {'--=_NextPart_' in mhtml_content}")
        print(f"      包含HTML: {'<!DOCTYPE html>' in mhtml_content}")
        print(f"      包含CSS样式: {'<style>' in mhtml_content}")
        print(f"      包含图片数据: {'Content-Type: image/' in mhtml_content}")
        
        # 显示MHTML内容的前500字符
        print(f"\n📝 MHTML内容预览（前500字符）:")
        print("-" * 50)
        print(mhtml_content[:500] + "...")
        print("-" * 50)
        
        # 测试保存功能
        print(f"\n💾 测试保存功能:")
        await mhtml_store.save_data_to_mhtml(test_weibo_data)
        
        # 验证文件是否成功保存
        if os.path.exists(save_file_name):
            file_size = os.path.getsize(save_file_name)
            print(f"   ✅ MHTML文件保存成功: {save_file_name}")
            print(f"   📁 文件大小: {file_size} bytes ({file_size/1024:.1f} KB)")
            
            # 验证文件内容
            with open(save_file_name, 'r', encoding='utf-8') as f:
                saved_content = f.read()
                print(f"   📊 保存的文件统计:")
                print(f"      文件长度: {len(saved_content)} 字符")
                print(f"      包含图片: {saved_content.count('Content-Type: image/')} 个")
        else:
            print(f"   ❌ MHTML文件保存失败")
        
        print(f"\n🎯 测试结果总结:")
        print(f"   ✅ MHTML存储模块初始化成功")
        print(f"   ✅ 文件路径生成正确")
        print(f"   ✅ MHTML内容生成成功")
        print(f"   ✅ 图片base64编码正常")
        print(f"   ✅ 文件保存功能正常")
        
        print(f"\n📋 使用说明:")
        print(f"   1. 将配置文件中的 SAVE_DATA_OPTION 设置为 'mhtml'")
        print(f"   2. 确保 ENABLE_GET_MEDIAS = True")
        print(f"   3. 运行微博爬虫：python main.py --platform wb --lt qrcode --type detail")
        print(f"   4. 生成的MHTML文件可在浏览器中直接打开查看")
        
        print(f"\n🌐 浏览器测试:")
        print(f"   可以在浏览器中打开以下文件进行测试:")
        print(f"   {os.path.abspath(save_file_name)}")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


async def test_mhtml_integration():
    """测试MHTML与现有存储系统的集成"""
    
    print(f"\n" + "=" * 60)
    print("MHTML集成测试")
    print("=" * 60)
    
    # 测试工厂模式
    from store.weibo import WeibostoreFactory
    
    # 临时修改配置进行测试
    original_save_option = config.SAVE_DATA_OPTION
    config.SAVE_DATA_OPTION = "mhtml"
    
    try:
        # 创建MHTML存储实例
        store = WeibostoreFactory.create_store()
        print(f"✅ 工厂模式创建MHTML存储成功: {type(store).__name__}")
        
        # 测试存储接口
        test_data = {
            "note_id": "test_integration",
            "nickname": "测试用户",
            "user_id": "test_user",
            "content": "这是一个集成测试的微博内容",
            "created_at": utils.get_current_date(),
            "attitudes_count": 0,
            "comments_count": 0,
            "reposts_count": 0,
            "local_image_paths": []
        }
        
        await store.store_content(test_data)
        print(f"✅ 存储接口调用成功")
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
    finally:
        # 恢复原始配置
        config.SAVE_DATA_OPTION = original_save_option


if __name__ == "__main__":
    asyncio.run(test_weibo_mhtml_functionality())
    asyncio.run(test_mhtml_integration())
