# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：
# 1. 不得用于任何商业用途。
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。
# 3. 不得进行大规模爬取或对平台造成运营干扰。
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。
# 5. 不得用于任何非法或不当的用途。
#
# 详细许可条款请参阅项目根目录下的LICENSE文件。
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。

# -*- coding: utf-8 -*-
# <AUTHOR> <EMAIL>
# @Time    : 2024/1/14 21:34
# @Desc    :

import re
import urllib.parse
from typing import Dict, List

import config
from tools import utils
from var import source_keyword_var

from .weibo_store_media import *
from .weibo_store_video import *
from .weibo_store_impl import *
from .weibo_store_mhtml import *


def _process_weibo_text_with_images(content_text: str) -> str:
    """
    处理微博文本内容，将图片链接替换为Markdown格式的本地图片链接
    :param content_text: 原始微博文本内容
    :return: 处理后的文本内容
    """
    if not content_text:
        return ""

    # 首先移除HTML标签，但保留图片链接信息
    text = content_text

    # 定义图片链接的正则表达式模式
    image_patterns = [
        # URL编码的图片链接（在href属性中）
        r'<a[^>]*href="[^"]*https%3A%2F%2Fwx(\d+)\.sinaimg\.cn%2Flarge%2F([^"\'>\s&]+)"[^>]*>.*?<span[^>]*>([^<]*)</span></a>',
        r'<a[^>]*href="[^"]*https%3A%2F%2F(\w+)\.sinaimg\.cn%2Flarge%2F([^"\'>\s&]+)"[^>]*>.*?<span[^>]*>([^<]*)</span></a>',
        # 直接的图片链接
        r'<a[^>]*href="[^"]*https://wx(\d+)\.sinaimg\.cn/large/([^"\'>\s]+)"[^>]*>.*?<span[^>]*>([^<]*)</span></a>',
        r'<a[^>]*href="[^"]*https://(\w+)\.sinaimg\.cn/large/([^"\'>\s]+)"[^>]*>.*?<span[^>]*>([^<]*)</span></a>',
    ]

    image_counter = 1

    for pattern in image_patterns:
        matches = re.finditer(pattern, text)
        for match in matches:
            full_match = match.group(0)

            if '%3A%2F%2F' in pattern:
                # URL编码的情况
                if 'wx' in pattern:
                    domain_num = match.group(1)
                    image_id = urllib.parse.unquote(match.group(2))
                    link_text = match.group(3)
                else:
                    domain = match.group(1)
                    image_id = urllib.parse.unquote(match.group(2))
                    link_text = match.group(3)
            else:
                # 直接链接的情况
                if 'wx' in pattern:
                    domain_num = match.group(1)
                    image_id = match.group(2)
                    link_text = match.group(3)
                else:
                    domain = match.group(1)
                    image_id = match.group(2)
                    link_text = match.group(3)

            # 构建本地图片路径
            # 从image_id中提取文件名（去掉可能的参数）
            if '.' in image_id:
                pic_filename = image_id.split('.')[0]
                extension = image_id.split('.')[-1]
            else:
                pic_filename = image_id
                extension = "jpg"

            # 构建Markdown图片链接
            local_image_path = f"../../images/{pic_filename}.{extension}"
            markdown_image = f"![图片{image_counter}]({local_image_path})"

            # 替换原始的HTML链接
            text = text.replace(full_match, markdown_image)
            image_counter += 1

    # 移除剩余的HTML标签
    clean_text = re.sub(r"<.*?>", "", text)

    return clean_text


class WeibostoreFactory:
    STORES = {
        "csv": WeiboCsvStoreImplement,
        "db": WeiboDbStoreImplement,
        "json": WeiboJsonStoreImplement,
        "sqlite": WeiboSqliteStoreImplement,
        "md": WeiboMarkdownStoreImplement,
        "mhtml": WeiboMhtmlStoreImplement,
    }

    @staticmethod
    def create_store() -> AbstractStore:
        store_class = WeibostoreFactory.STORES.get(config.SAVE_DATA_OPTION)
        if not store_class:
            raise ValueError("[WeibotoreFactory.create_store] Invalid save option only supported csv or db or json or sqlite or md or mhtml ...")
        return store_class()


async def batch_update_weibo_notes(note_list: List[Dict]):
    """
    Batch update weibo notes
    Args:
        note_list:

    Returns:

    """
    if not note_list:
        return
    for note_item in note_list:
        await update_weibo_note(note_item)


async def update_weibo_note(note_item: Dict):
    """
    Update weibo note
    Args:
        note_item:

    Returns:

    """
    if not note_item:
        return

    mblog: Dict = note_item.get("mblog")
    user_info: Dict = mblog.get("user")
    note_id = mblog.get("id")
    content_text = mblog.get("text")

    # 处理文本中的图片链接，将其替换为Markdown格式的本地图片链接
    clean_text = _process_weibo_text_with_images(content_text)

    # 处理图片信息
    pics = mblog.get("pics", [])
    image_urls = []
    local_image_paths = []

    if pics:
        for pic in pics:
            # 原始图片URL
            if pic.get("url"):
                image_urls.append(pic.get("url"))
            # 本地图片路径（基于pic_id）
            if pic.get("pid"):
                # 根据微博图片存储逻辑构建本地路径
                pic_id = pic.get("pid")
                # 从URL中提取文件扩展名，默认为jpg
                extension = "jpg"
                if pic.get("url"):
                    extension = pic.get("url").split(".")[-1] if "." in pic.get("url") else "jpg"
                local_path = f"data/weibo/images/{pic_id}.{extension}"
                local_image_paths.append(local_path)

    # 处理视频信息
    video_urls = []
    local_video_paths = []
    video_info = mblog.get("page_info", {})

    # 检查是否包含视频
    if video_info and video_info.get("type") == "video":
        # 提取视频URL
        media_info = video_info.get("media_info", {})
        if media_info:
            # 尝试获取不同质量的视频URL
            video_url = (media_info.get("mp4_720p_mp4") or
                        media_info.get("mp4_hd_url") or
                        media_info.get("mp4_sd_url") or
                        media_info.get("stream_url"))

            if video_url:
                video_urls.append(video_url)
                # 构建本地视频路径
                video_id = video_info.get("object_id", note_id)
                local_video_path = f"data/weibo/videos/{video_id}.mp4"
                local_video_paths.append(local_video_path)

    save_content_item = {
        # 微博信息
        "note_id": note_id,
        "content": clean_text,
        "create_time": utils.rfc2822_to_timestamp(mblog.get("created_at")),
        "create_date_time": str(utils.rfc2822_to_china_datetime(mblog.get("created_at"))),
        "liked_count": str(mblog.get("attitudes_count", 0)),
        "comments_count": str(mblog.get("comments_count", 0)),
        "shared_count": str(mblog.get("reposts_count", 0)),
        "last_modify_ts": utils.get_current_timestamp(),
        "note_url": f"https://m.weibo.cn/detail/{note_id}",
        "ip_location": mblog.get("region_name", "").replace("发布于 ", ""),

        # 图片信息
        "image_urls": image_urls,
        "local_image_paths": local_image_paths,
        "image_count": len(pics),

        # 视频信息
        "video_urls": video_urls,
        "local_video_paths": local_video_paths,
        "video_count": len(video_urls),

        # 用户信息
        "user_id": str(user_info.get("id")),
        "nickname": user_info.get("screen_name", ""),
        "gender": user_info.get("gender", ""),
        "profile_url": user_info.get("profile_url", ""),
        "avatar": user_info.get("profile_image_url", ""),
        "source_keyword": source_keyword_var.get(),
    }
    utils.logger.info(f"[store.weibo.update_weibo_note] weibo note id:{note_id}, title:{save_content_item.get('content')[:24]} ...")
    await WeibostoreFactory.create_store().store_content(content_item=save_content_item)


async def batch_update_weibo_note_comments(note_id: str, comments: List[Dict]):
    """
    Batch update weibo note comments
    Args:
        note_id:
        comments:

    Returns:

    """
    if not comments:
        return
    for comment_item in comments:
        await update_weibo_note_comment(note_id, comment_item)


async def update_weibo_note_comment(note_id: str, comment_item: Dict):
    """
    Update weibo note comment
    Args:
        note_id: weibo note id
        comment_item: weibo comment item

    Returns:

    """
    if not comment_item or not note_id:
        return
    comment_id = str(comment_item.get("id"))
    user_info: Dict = comment_item.get("user")
    content_text = comment_item.get("text")
    clean_text = re.sub(r"<.*?>", "", content_text)
    save_comment_item = {
        "comment_id": comment_id,
        "create_time": utils.rfc2822_to_timestamp(comment_item.get("created_at")),
        "create_date_time": str(utils.rfc2822_to_china_datetime(comment_item.get("created_at"))),
        "note_id": note_id,
        "content": clean_text,
        "sub_comment_count": str(comment_item.get("total_number", 0)),
        "comment_like_count": str(comment_item.get("like_count", 0)),
        "last_modify_ts": utils.get_current_timestamp(),
        "ip_location": comment_item.get("source", "").replace("来自", ""),
        "parent_comment_id": comment_item.get("rootid", ""),

        # 用户信息
        "user_id": str(user_info.get("id")),
        "nickname": user_info.get("screen_name", ""),
        "gender": user_info.get("gender", ""),
        "profile_url": user_info.get("profile_url", ""),
        "avatar": user_info.get("profile_image_url", ""),
    }
    utils.logger.info(f"[store.weibo.update_weibo_note_comment] Weibo note comment: {comment_id}, content: {save_comment_item.get('content', '')[:24]} ...")
    await WeibostoreFactory.create_store().store_comment(comment_item=save_comment_item)


async def update_weibo_note_image(picid: str, pic_content, extension_file_name):
    """
    Save weibo note image to local
    Args:
        picid:
        pic_content:
        extension_file_name:

    Returns:

    """
    await WeiboStoreImage().store_image({"pic_id": picid, "pic_content": pic_content, "extension_file_name": extension_file_name})


async def update_weibo_note_video(video_id: str, video_content: bytes, extension_file_name: str):
    """
    Save weibo note video to local
    Args:
        video_id: 视频ID
        video_content: 视频内容
        extension_file_name: 文件扩展名

    Returns:

    """
    await WeiboStoreVideo().store_video({"video_id": video_id, "video_content": video_content, "extension_file_name": extension_file_name})


async def save_creator(user_id: str, user_info: Dict):
    """
    Save creator information to local
    Args:
        user_id:
        user_info:

    Returns:

    """
    local_db_item = {
        'user_id': user_id,
        'nickname': user_info.get('screen_name'),
        'gender': '女' if user_info.get('gender') == "f" else '男',
        'avatar': user_info.get('avatar_hd'),
        'desc': user_info.get('description'),
        'ip_location': user_info.get("source", "").replace("来自", ""),
        'follows': user_info.get('follow_count', ''),
        'fans': user_info.get('followers_count', ''),
        'tag_list': '',
        "last_modify_ts": utils.get_current_timestamp(),
    }
    utils.logger.info(f"[store.weibo.save_creator] creator:{local_db_item}")
    await WeibostoreFactory.create_store().store_creator(local_db_item)
