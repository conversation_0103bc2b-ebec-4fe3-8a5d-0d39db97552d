# 微博MHTML保存功能使用说明

## 📋 功能概述

微博MHTML保存功能允许将微博内容保存为MHTML（MIME HTML）格式，这是一种网页归档格式，可以将完整的网页内容（包括HTML、CSS、图片等资源）保存在单个文件中，便于离线查看和长期保存。

## 🎯 主要特性

- **完整内容保存**：包含微博文本、图片、样式等所有内容
- **离线查看**：生成的MHTML文件可在任何支持的浏览器中直接打开
- **嵌入式资源**：图片等媒体资源以base64格式嵌入，无需外部依赖
- **美观展示**：包含专门设计的CSS样式，提供良好的阅读体验
- **元数据保留**：保存点赞数、评论数、转发数等完整信息

## ⚙️ 配置方法

### 1. 修改配置文件

编辑 `config/base_config.py` 文件：

```python
# 设置数据保存格式为MHTML
SAVE_DATA_OPTION = "mhtml"

# 确保启用媒体资源下载（必需）
ENABLE_GET_MEDIAS = True
```

### 2. 支持的配置选项

- `SAVE_DATA_OPTION = "mhtml"` - 启用MHTML保存格式
- `ENABLE_GET_MEDIAS = True` - 必须启用，用于下载和嵌入图片资源
- 其他微博爬虫配置保持不变

## 🚀 使用方法

### 1. 基本使用

```bash
# 设置配置后，正常运行微博爬虫
python main.py --platform wb --lt qrcode --type detail
```

### 2. 指定微博ID爬取

在 `config/weibo_config.py` 中设置：

```python
WEIBO_SPECIFIED_ID_LIST = ["Q1ufxC7ce", "其他微博ID"]
```

然后运行爬虫。

### 3. 关键词搜索

```python
# 在配置文件中设置搜索关键词
KEYWORDS = ["关键词1", "关键词2"]
```

## 📁 文件结构

生成的MHTML文件按以下结构保存：

```
data/weibo/mhtml/
├── 用户名_用户ID/
│   ├── 微博ID1.mhtml
│   ├── 微博ID2.mhtml
│   └── ...
└── 其他用户文件夹/
```

### 示例路径

```
data/weibo/mhtml/止谣君_1837234894/5203546119084130.mhtml
```

## 🌐 MHTML文件特性

### 1. 文件内容

每个MHTML文件包含：

- **HTML结构**：完整的网页布局
- **CSS样式**：美观的显示样式
- **嵌入图片**：base64编码的图片数据
- **元数据**：微博的完整信息

### 2. 浏览器兼容性

支持的浏览器：
- ✅ Chrome/Chromium
- ✅ Microsoft Edge
- ✅ Firefox（部分支持）
- ✅ Safari（部分支持）

### 3. 文件大小

- 纯文本微博：通常 < 50KB
- 包含图片的微博：根据图片数量和大小，通常 100KB - 5MB

## 🧪 测试验证

### 1. 运行测试脚本

```bash
python test_weibo_mhtml.py
```

测试脚本会验证：
- MHTML生成功能
- 图片嵌入功能
- 文件保存功能
- 集成测试

### 2. 手动验证

1. 运行爬虫生成MHTML文件
2. 在浏览器中打开生成的.mhtml文件
3. 验证内容显示是否完整
4. 检查图片是否正常显示

## 🔧 技术实现

### 1. 核心组件

- `WeiboMhtmlStoreImplement`：MHTML存储实现类
- `WeibostoreFactory`：存储工厂，支持MHTML格式
- `generate_mhtml_content()`：MHTML内容生成方法

### 2. 关键技术

- **MIME多部分格式**：标准的MHTML格式
- **Base64编码**：图片资源嵌入
- **CSS样式**：美观的页面展示
- **异步处理**：高效的文件操作

### 3. 依赖库

- `aiofiles`：异步文件操作
- `base64`：图片编码
- `mimetypes`：MIME类型检测

## 📊 性能考虑

### 1. 文件大小

- MHTML文件会比原始数据大（因为包含完整的HTML和嵌入资源）
- 建议定期清理不需要的MHTML文件

### 2. 生成速度

- 图片较多的微博生成时间较长
- 建议合理设置并发数量

### 3. 存储空间

- 每个微博生成独立的MHTML文件
- 注意磁盘空间使用情况

## 🛠️ 故障排除

### 1. 常见问题

**Q: MHTML文件无法在浏览器中打开？**
A: 确保文件扩展名为.mhtml，某些浏览器可能需要手动选择打开方式。

**Q: 图片不显示？**
A: 检查 `ENABLE_GET_MEDIAS` 是否设置为 `True`，确保图片已正确下载。

**Q: 文件生成失败？**
A: 检查磁盘空间和文件权限，查看日志输出获取详细错误信息。

### 2. 调试方法

1. 查看日志输出：
```bash
tail -f logs/crawler.log
```

2. 运行测试脚本验证功能：
```bash
python test_weibo_mhtml.py
```

3. 检查生成的文件：
```bash
ls -la data/weibo/mhtml/
```

## 📝 注意事项

1. **版权问题**：请遵守相关法律法规，仅用于个人学习和研究
2. **存储空间**：MHTML文件较大，注意磁盘空间管理
3. **更新维护**：定期更新爬虫以适应平台变化
4. **隐私保护**：注意保护个人隐私信息

## 🔄 版本更新

- v1.0.0：基础MHTML保存功能
- 后续版本将根据用户反馈持续改进

## 📞 技术支持

如有问题或建议，请：
1. 查看日志文件获取详细错误信息
2. 运行测试脚本验证功能
3. 检查配置文件设置
4. 参考本文档的故障排除部分
