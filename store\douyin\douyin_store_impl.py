# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：  
# 1. 不得用于任何商业用途。  
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。  
# 3. 不得进行大规模爬取或对平台造成运营干扰。  
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。   
# 5. 不得用于任何非法或不当的用途。
#   
# 详细许可条款请参阅项目根目录下的LICENSE文件。  
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。  


# -*- coding: utf-8 -*-
# <AUTHOR> <EMAIL>
# @Time    : 2024/1/14 18:46
# @Desc    : 抖音存储实现类
import asyncio
import csv
import json
import os
import pathlib
from typing import Dict

import aiofiles

import config
from base.base_crawler import AbstractStore
from tools import utils, words
from var import crawler_type_var


def calculate_number_of_files(file_store_path: str) -> int:
    """计算数据保存文件的前部分排序数字，支持每次运行代码不写到同一个文件中
    Args:
        file_store_path;
    Returns:
        file nums
    """
    if not os.path.exists(file_store_path):
        return 1
    try:
        return max([int(file_name.split("_")[0]) for file_name in os.listdir(file_store_path)]) + 1
    except ValueError:
        return 1


class DouyinCsvStoreImplement(AbstractStore):
    csv_store_path: str = "data/douyin"
    file_count: int = calculate_number_of_files(csv_store_path)

    def make_save_file_name(self, store_type: str) -> str:
        """
        make save file name by store type
        Args:
            store_type: contents or comments

        Returns: eg: data/douyin/search_comments_20240114.csv ...

        """
        return f"{self.csv_store_path}/{self.file_count}_{crawler_type_var.get()}_{store_type}_{utils.get_current_date()}.csv"

    async def save_data_to_csv(self, save_item: Dict, store_type: str):
        """
        Below is a simple way to save it in CSV format.
        Args:
            save_item:  save content dict info
            store_type: Save type contains content and comments（contents | comments）

        Returns: no returns

        """
        pathlib.Path(self.csv_store_path).mkdir(parents=True, exist_ok=True)
        save_file_name = self.make_save_file_name(store_type=store_type)
        async with aiofiles.open(save_file_name, mode='a+', encoding="utf-8-sig", newline="") as f:
            writer = csv.writer(f)
            if await f.tell() == 0:
                await writer.writerow(save_item.keys())
            await writer.writerow(save_item.values())

    async def store_content(self, content_item: Dict):
        """
        Douyin content CSV storage implementation
        Args:
            content_item: note item dict

        Returns:

        """
        await self.save_data_to_csv(save_item=content_item, store_type="contents")

    async def store_comment(self, comment_item: Dict):
        """
        Douyin comment CSV storage implementation
        Args:
            comment_item: comment item dict

        Returns:

        """
        await self.save_data_to_csv(save_item=comment_item, store_type="comments")

    async def store_creator(self, creator: Dict):
        """
        Douyin creator CSV storage implementation
        Args:
            creator: creator item dict

        Returns:

        """
        await self.save_data_to_csv(save_item=creator, store_type="creator")


class DouyinDbStoreImplement(AbstractStore):
    async def store_content(self, content_item: Dict):
        """
        Douyin content DB storage implementation
        Args:
            content_item: content item dict

        Returns:

        """

        from .douyin_store_sql import (add_new_content,
                                       query_content_by_content_id,
                                       update_content_by_content_id)
        aweme_id = content_item.get("aweme_id")
        aweme_detail: Dict = await query_content_by_content_id(content_id=aweme_id)
        if not aweme_detail:
            content_item["add_ts"] = utils.get_current_timestamp()
            if content_item.get("title"):
                await add_new_content(content_item)
        else:
            await update_content_by_content_id(aweme_id, content_item=content_item)

    async def store_comment(self, comment_item: Dict):
        """
        Douyin content DB storage implementation
        Args:
            comment_item: comment item dict

        Returns:

        """
        from .douyin_store_sql import (add_new_comment,
                                       query_comment_by_comment_id,
                                       update_comment_by_comment_id)
        comment_id = comment_item.get("comment_id")
        comment_detail: Dict = await query_comment_by_comment_id(comment_id=comment_id)
        if not comment_detail:
            comment_item["add_ts"] = utils.get_current_timestamp()
            await add_new_comment(comment_item)
        else:
            await update_comment_by_comment_id(comment_id, comment_item=comment_item)

    async def store_creator(self, creator: Dict):
        """
        Douyin content DB storage implementation
        Args:
            creator: creator dict

        Returns:

        """
        from .douyin_store_sql import (add_new_creator,
                                       query_creator_by_user_id,
                                       update_creator_by_user_id)
        user_id = creator.get("user_id")
        user_detail: Dict = await query_creator_by_user_id(user_id)
        if not user_detail:
            creator["add_ts"] = utils.get_current_timestamp()
            await add_new_creator(creator)
        else:
            await update_creator_by_user_id(user_id, creator)

class DouyinJsonStoreImplement(AbstractStore):
    json_store_path: str = "data/douyin/json"
    words_store_path: str = "data/douyin/words"

    lock = asyncio.Lock()
    file_count: int = calculate_number_of_files(json_store_path)
    WordCloud = words.AsyncWordCloudGenerator()

    def make_save_file_name(self, store_type: str) -> (str,str):
        """
        make save file name by store type
        Args:
            store_type: Save type contains content and comments（contents | comments）

        Returns:

        """

        return (
            f"{self.json_store_path}/{crawler_type_var.get()}_{store_type}_{utils.get_current_date()}.json",
            f"{self.words_store_path}/{crawler_type_var.get()}_{store_type}_{utils.get_current_date()}"
        )
    async def save_data_to_json(self, save_item: Dict, store_type: str):
        """
        Below is a simple way to save it in json format.
        Args:
            save_item: save content dict info
            store_type: Save type contains content and comments（contents | comments）

        Returns:

        """
        pathlib.Path(self.json_store_path).mkdir(parents=True, exist_ok=True)
        pathlib.Path(self.words_store_path).mkdir(parents=True, exist_ok=True)
        save_file_name,words_file_name_prefix = self.make_save_file_name(store_type=store_type)
        save_data = []

        async with self.lock:
            if os.path.exists(save_file_name):
                async with aiofiles.open(save_file_name, 'r', encoding='utf-8') as file:
                    save_data = json.loads(await file.read())

            save_data.append(save_item)
            async with aiofiles.open(save_file_name, 'w', encoding='utf-8') as file:
                await file.write(json.dumps(save_data, ensure_ascii=False))

            if config.ENABLE_GET_COMMENTS and config.ENABLE_GET_WORDCLOUD:
                try:
                    await self.WordCloud.generate_word_frequency_and_cloud(save_data, words_file_name_prefix)
                except:
                    pass

    async def store_content(self, content_item: Dict):
        """
        content JSON storage implementation
        Args:
            content_item:

        Returns:

        """
        await self.save_data_to_json(content_item, "contents")

    async def store_comment(self, comment_item: Dict):
        """
        comment JSON storage implementation
        Args:
            comment_item:

        Returns:

        """
        await self.save_data_to_json(comment_item, "comments")


    async def store_creator(self, creator: Dict):
        """
        Douyin creator CSV storage implementation
        Args:
            creator: creator item dict

        Returns:

        """
        await self.save_data_to_json(save_item=creator, store_type="creator")


class DouyinSqliteStoreImplement(AbstractStore):
    async def store_content(self, content_item: Dict):
        """
        Douyin content SQLite storage implementation
        Args:
            content_item: content item dict

        Returns:

        """

        from .douyin_store_sql import (add_new_content,
                                       query_content_by_content_id,
                                       update_content_by_content_id)
        aweme_id = content_item.get("aweme_id")
        aweme_detail: Dict = await query_content_by_content_id(content_id=aweme_id)
        if not aweme_detail:
            content_item["add_ts"] = utils.get_current_timestamp()
            if content_item.get("title"):
                await add_new_content(content_item)
        else:
            await update_content_by_content_id(aweme_id, content_item=content_item)

    async def store_comment(self, comment_item: Dict):
        """
        Douyin comment SQLite storage implementation
        Args:
            comment_item: comment item dict

        Returns:

        """
        from .douyin_store_sql import (add_new_comment,
                                       query_comment_by_comment_id,
                                       update_comment_by_comment_id)
        comment_id = comment_item.get("comment_id")
        comment_detail: Dict = await query_comment_by_comment_id(comment_id=comment_id)
        if not comment_detail:
            comment_item["add_ts"] = utils.get_current_timestamp()
            await add_new_comment(comment_item)
        else:
            await update_comment_by_comment_id(comment_id, comment_item=comment_item)

    async def store_creator(self, creator: Dict):
        """
        Douyin creator SQLite storage implementation
        Args:
            creator: creator dict

        Returns:

        """
        from .douyin_store_sql import (add_new_creator,
                                       query_creator_by_user_id,
                                       update_creator_by_user_id)
        user_id = creator.get("user_id")
        user_detail: Dict = await query_creator_by_user_id(user_id)
        if not user_detail:
            creator["add_ts"] = utils.get_current_timestamp()
            await add_new_creator(creator)
        else:
            await update_creator_by_user_id(user_id, creator)


class DouyinMarkdownStoreImplement(AbstractStore):
    md_store_path: str = "data/douyin/md"
    file_count: int = calculate_number_of_files(md_store_path)

    def sanitize_filename(self, filename: str) -> str:
        """
        清理文件名中的特殊字符
        Args:
            filename: 原始文件名

        Returns: 清理后的文件名
        """
        # 替换特殊字符为下划线
        import re
        return re.sub(r'[<>:"/\\|?*]', '_', filename)

    def make_save_file_name(self, save_item: Dict) -> str:
        """
        根据用户信息和帖子ID生成文件路径
        Args:
            save_item: 包含用户信息和帖子信息的字典

        Returns: eg: data/douyin/md/用户名_用户ID/帖子ID.md
        """
        # 获取用户信息
        nickname = save_item.get("nickname", "未知用户")
        user_id = save_item.get("user_id", "unknown")
        note_id = save_item.get("note_id", "unknown")

        # 清理用户名中的特殊字符
        clean_nickname = self.sanitize_filename(nickname)

        # 构建用户文件夹路径
        user_folder = f"{clean_nickname}_{user_id}"
        user_path = f"{self.md_store_path}/{user_folder}"

        # 确保用户文件夹存在
        pathlib.Path(user_path).mkdir(parents=True, exist_ok=True)

        # 返回完整文件路径
        return f"{user_path}/{note_id}.md"

    async def save_data_to_md(self, save_item: Dict, store_type: str):
        """
        将数据保存为Markdown格式，每个帖子一个文件，包含所有相关信息
        Args:
            save_item: save content dict info
            store_type: Save type contains content and comments（contents | comments | creators）

        Returns: no returns
        """
        save_file_name = self.make_save_file_name(save_item, store_type)

        # 检查文件是否存在，如果不存在则创建标题
        file_exists = os.path.exists(save_file_name)

        async with aiofiles.open(save_file_name, mode='a+', encoding="utf-8") as f:
            if not file_exists:
                # 写入文档标题
                note_id = save_item.get("note_id", "unknown")
                nickname = save_item.get("nickname", "未知用户")
                await f.write(f"# 抖音视频 {note_id} - {nickname}\n\n")
                await f.write(f"*生成时间: {utils.get_current_date()}*\n\n")

            # 根据数据类型写入不同的分区
            if store_type == "contents":
                await f.write("## 📹 视频内容\n\n")
            elif store_type == "comments":
                await f.write("## 💬 评论信息\n\n")
            elif store_type == "creators":
                await f.write("## 👤 创作者信息\n\n")

            # 写入数据项
            await f.write("---\n\n")
            for key, value in save_item.items():
                # 特殊处理图片相关字段
                if key == "local_image_paths" and isinstance(value, list) and value:
                    await f.write(f"**本地图片**: \n")
                    for i, img_path in enumerate(value, 1):
                        # 转换为相对路径：从用户文件夹访问images目录
                        relative_path = img_path.replace("data/douyin/images/", "../../images/")
                        # 使用Markdown图片语法
                        await f.write(f"  {i}. ![图片{i}]({relative_path})\n")
                    await f.write("\n")
                elif key == "image_urls":
                    # 跳过image_urls字段，不在Markdown中显示
                    continue
                elif key == "image_count":
                    # 显示图片数量
                    await f.write(f"**图片数量**: {value}\n\n")
                else:
                    # 处理特殊字符，避免破坏markdown格式
                    if isinstance(value, str):
                        value = value.replace('\n', '\\n').replace('|', '\\|')
                    elif isinstance(value, list):
                        # 将列表转换为字符串
                        value = str(value)
                    await f.write(f"**{key}**: {value}\n\n")
            await f.write("\n")

    async def store_content(self, content_item: Dict):
        """
        Douyin content Markdown storage implementation
        Args:
            content_item: note item dict

        Returns:

        """
        await self.save_data_to_md(save_item=content_item, store_type="contents")

    async def store_comment(self, comment_item: Dict):
        """
        Douyin comment Markdown storage implementation
        Args:
            comment_item: comment item dict

        Returns:

        """
        await self.save_data_to_md(save_item=comment_item, store_type="comments")

    async def store_creator(self, creator: Dict):
        """
        Douyin creator Markdown storage implementation
        Args:
            creator: creator dict

        Returns:

        """
        await self.save_data_to_md(save_item=creator, store_type="creators")