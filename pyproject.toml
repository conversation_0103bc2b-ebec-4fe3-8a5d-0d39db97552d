[project]
name = "mediacrawler"
author = "程序员阿江-<PERSON><PERSON><PERSON> <<EMAIL>>"
version = "0.1.0"
description = "A social media crawler project, support Xiaohongshu, Weibo, Zhihu, Bilibili, <PERSON><PERSON><PERSON>, BaiduTieBa etc."
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "aiofiles~=23.2.1",
    "aiomysql==0.2.0",
    "aiosqlite>=0.21.0",
    "fastapi==0.110.2",
    "httpx==0.28.1",
    "jieba==0.42.1",
    "matplotlib==3.9.0",
    "opencv-python>=*********",
    "pandas==2.2.3",
    "parsel==1.9.1",
    "pillow==9.5.0",
    "playwright==1.45.0",
    "pydantic==2.5.2",
    "pyexecjs==1.5.1",
    "python-dotenv==1.0.1",
    "redis~=4.6.0",
    "requests==2.32.3",
    "tenacity==8.2.2",
    "uvicorn==0.29.0",
    "wordcloud==1.9.3",
]

[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true
