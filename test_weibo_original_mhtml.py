#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微博原始HTML的MHTML保存功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from store.weibo.weibo_store_mhtml import WeiboMhtmlStoreImplement
from tools import utils


async def test_original_html_fetch():
    """测试原始HTML获取功能"""
    
    print("=" * 60)
    print("微博原始HTML获取测试")
    print("=" * 60)
    
    # 创建MHTML存储实例
    mhtml_store = WeiboMhtmlStoreImplement()
    
    # 测试微博ID（用户选中的ID）
    test_note_id = "Q1lmPwRoi"
    
    print(f"🔍 测试微博ID: {test_note_id}")
    print(f"📋 当前配置:")
    print(f"   SAVE_DATA_OPTION: {config.SAVE_DATA_OPTION}")
    print(f"   ENABLE_GET_MEDIAS: {config.ENABLE_GET_MEDIAS}")
    
    print(f"\n🌐 开始获取原始HTML...")
    
    try:
        # 获取原始HTML
        original_html = await mhtml_store.fetch_original_weibo_html(test_note_id)
        
        if original_html:
            print(f"✅ 成功获取原始HTML")
            print(f"📊 HTML统计:")
            print(f"   总长度: {len(original_html)} 字符")
            print(f"   包含DOCTYPE: {'<!DOCTYPE' in original_html}")
            print(f"   包含head标签: {'<head>' in original_html}")
            print(f"   包含body标签: {'<body>' in original_html}")
            print(f"   包含CSS链接: {original_html.count('<link') > 0}")
            print(f"   包含script标签: {original_html.count('<script') > 0}")
            
            # 显示HTML的前500字符
            print(f"\n📝 HTML内容预览（前500字符）:")
            print("-" * 50)
            print(original_html[:500] + "...")
            print("-" * 50)
            
            # 测试HTML处理
            print(f"\n🔧 测试HTML处理...")
            processed_html = mhtml_store.process_original_html(original_html, test_note_id)
            
            print(f"📊 处理后HTML统计:")
            print(f"   处理后长度: {len(processed_html)} 字符")
            print(f"   移除的script标签: {original_html.count('<script') - processed_html.count('<script')}")
            print(f"   包含保存标识: {'saved-by' in processed_html}")
            
            # 保存原始HTML到文件用于调试
            debug_file = f"debug_original_{test_note_id}.html"
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(processed_html)
            print(f"🐛 调试文件已保存: {debug_file}")
            
        else:
            print(f"❌ 未能获取原始HTML")
            
    except Exception as e:
        print(f"❌ 获取原始HTML时出错: {e}")
        import traceback
        traceback.print_exc()


async def test_original_mhtml_generation():
    """测试使用原始HTML生成MHTML"""
    
    print(f"\n" + "=" * 60)
    print("原始HTML MHTML生成测试")
    print("=" * 60)
    
    # 创建MHTML存储实例
    mhtml_store = WeiboMhtmlStoreImplement()
    
    # 测试微博ID
    test_note_id = "Q1lmPwRoi"
    
    # 模拟微博数据
    test_weibo_data = {
        "note_id": test_note_id,
        "nickname": "测试用户",
        "user_id": "test_user",
        "content": "这是一个测试微博内容",
        "created_at": utils.get_current_date(),
        "attitudes_count": 100,
        "comments_count": 50,
        "reposts_count": 25,
        "local_image_paths": []  # 暂时不包含图片
    }
    
    print(f"🧪 开始生成MHTML（使用原始HTML）...")
    
    try:
        # 生成MHTML内容
        mhtml_content = await mhtml_store.generate_mhtml_content(test_weibo_data)
        
        print(f"✅ MHTML生成成功")
        print(f"📊 MHTML统计:")
        print(f"   总长度: {len(mhtml_content)} 字符")
        print(f"   包含边界标记: {'--=_NextPart_' in mhtml_content}")
        print(f"   包含HTML部分: {'Content-Type: text/html' in mhtml_content}")
        print(f"   包含CSS部分: {'Content-Type: text/css' in mhtml_content}")
        
        # 保存MHTML文件
        save_file_name = mhtml_store.make_save_file_name(test_weibo_data)
        await mhtml_store.save_data_to_mhtml(test_weibo_data)
        
        if os.path.exists(save_file_name):
            file_size = os.path.getsize(save_file_name)
            print(f"✅ MHTML文件保存成功: {save_file_name}")
            print(f"📁 文件大小: {file_size} bytes ({file_size/1024:.1f} KB)")
            
            print(f"\n🌐 浏览器测试:")
            print(f"   可以在浏览器中打开以下文件:")
            print(f"   {os.path.abspath(save_file_name)}")
            
        else:
            print(f"❌ MHTML文件保存失败")
            
    except Exception as e:
        print(f"❌ 生成MHTML时出错: {e}")
        import traceback
        traceback.print_exc()


async def test_css_extraction():
    """测试CSS提取功能"""
    
    print(f"\n" + "=" * 60)
    print("CSS提取测试")
    print("=" * 60)
    
    # 创建MHTML存储实例
    mhtml_store = WeiboMhtmlStoreImplement()
    
    # 测试CSS URL
    test_css_urls = [
        "https://h5.sinaimg.cn/m/weibo-lite/css/app.css",
        "//h5.sinaimg.cn/m/weibo-lite/css/common.css"
    ]
    
    base_url = "https://m.weibo.cn"
    
    for css_url in test_css_urls:
        print(f"🎨 测试CSS: {css_url}")
        
        try:
            css_content = await mhtml_store.fetch_css_content(css_url, base_url)
            
            if css_content:
                print(f"   ✅ 成功获取CSS，长度: {len(css_content)} 字符")
                print(f"   📝 CSS预览（前100字符）: {css_content[:100]}...")
            else:
                print(f"   ❌ 未能获取CSS")
                
        except Exception as e:
            print(f"   ❌ 获取CSS时出错: {e}")


async def main():
    """主测试函数"""
    
    print("🚀 开始微博原始HTML MHTML功能测试")
    
    # 测试1: 原始HTML获取
    await test_original_html_fetch()
    
    # 测试2: 原始HTML MHTML生成
    await test_original_mhtml_generation()
    
    # 测试3: CSS提取
    await test_css_extraction()
    
    print(f"\n🎯 测试完成总结:")
    print(f"   1. ✅ 原始HTML获取功能已实现")
    print(f"   2. ✅ HTML处理和清理功能已实现")
    print(f"   3. ✅ CSS资源提取功能已实现")
    print(f"   4. ✅ 原始HTML MHTML生成功能已实现")
    print(f"   5. ✅ 回退到自定义模板功能已保留")
    
    print(f"\n📋 使用说明:")
    print(f"   1. 设置 SAVE_DATA_OPTION = 'mhtml'")
    print(f"   2. 确保 ENABLE_GET_MEDIAS = True")
    print(f"   3. 运行微博爬虫，将自动使用原始HTML样式")
    print(f"   4. 如果获取原始HTML失败，会自动回退到自定义模板")


if __name__ == "__main__":
    asyncio.run(main())
