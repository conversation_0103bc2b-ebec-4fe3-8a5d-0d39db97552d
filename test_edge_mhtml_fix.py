#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Edge浏览器MHTML兼容性修复
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from store.weibo.weibo_store_mhtml import WeiboMhtmlStoreImplement
from tools import utils


async def test_edge_compatibility_fixes():
    """测试Edge兼容性修复"""
    
    print("=" * 60)
    print("Edge浏览器MHTML兼容性修复测试")
    print("=" * 60)
    
    # 创建MHTML存储实例
    mhtml_store = WeiboMhtmlStoreImplement()
    
    print(f"📋 当前配置:")
    print(f"   SAVE_DATA_OPTION: {config.SAVE_DATA_OPTION}")
    print(f"   ENABLE_GET_MEDIAS: {config.ENABLE_GET_MEDIAS}")
    print(f"   MHTML_USE_ORIGINAL_STYLE: {config.MHTML_USE_ORIGINAL_STYLE}")
    
    # 测试quoted-printable编码
    print(f"\n🔧 测试quoted-printable编码:")
    test_text = "这是一个包含中文的测试文本，用于验证编码是否正确。Test with English text."
    encoded_text = mhtml_store.encode_quoted_printable(test_text)
    print(f"   原始文本长度: {len(test_text)}")
    print(f"   编码后长度: {len(encoded_text)}")
    print(f"   编码成功: {'=' in encoded_text or len(encoded_text) > 0}")
    
    # 测试HTML处理
    print(f"\n🔧 测试HTML处理:")
    test_html = """<!DOCTYPE html>
<html>
<head>
    <title>Test</title>
    <script>console.log('test');</script>
</head>
<body>
    <div class="m-text-box">微博内容</div>
    <div class="m-btn-box">点赞 评论 转发</div>
    <script>alert('test');</script>
</body>
</html>"""
    
    processed_html = mhtml_store.process_original_html(test_html, "test_id")
    print(f"   原始HTML长度: {len(test_html)}")
    print(f"   处理后HTML长度: {len(processed_html)}")
    print(f"   移除了script标签: {'<script>' not in processed_html}")
    print(f"   添加了Edge兼容性样式: {'Edge兼容性修复' in processed_html}")
    print(f"   添加了charset声明: {'charset=utf-8' in processed_html}")
    
    # 创建测试微博数据
    test_weibo_data = {
        "note_id": "edge_test_001",
        "nickname": "Edge测试用户",
        "user_id": "edge_test",
        "content": "这是一个用于测试Edge浏览器兼容性的微博内容。包含中文字符和特殊符号：@#$%^&*()",
        "created_at": utils.get_current_date(),
        "attitudes_count": 123,
        "comments_count": 45,
        "reposts_count": 67,
        "local_image_paths": []
    }
    
    print(f"\n🧪 测试MHTML生成:")
    
    try:
        # 强制使用自定义模板进行测试（避免网络请求）
        original_setting = config.MHTML_USE_ORIGINAL_STYLE
        config.MHTML_USE_ORIGINAL_STYLE = False
        
        # 生成MHTML内容
        mhtml_content = await mhtml_store.generate_mhtml_content(test_weibo_data)
        
        # 恢复原始设置
        config.MHTML_USE_ORIGINAL_STYLE = original_setting
        
        print(f"   ✅ MHTML生成成功")
        print(f"   📊 MHTML统计:")
        print(f"      总长度: {len(mhtml_content)} 字符")
        print(f"      包含X-MimeOLE头: {'X-MimeOLE' in mhtml_content}")
        print(f"      使用quoted-printable编码: {'Content-Transfer-Encoding: quoted-printable' in mhtml_content}")
        print(f"      包含charset声明: {'charset=utf-8' in mhtml_content}")
        print(f"      包含Edge兼容性样式: {'Edge兼容性修复' in mhtml_content}")
        
        # 检查MHTML格式正确性
        boundary_count = mhtml_content.count('--=_NextPart_')
        print(f"      边界标记数量: {boundary_count}")
        print(f"      格式正确: {boundary_count >= 2}")  # 至少应该有开始和结束标记
        
        # 保存测试文件
        save_file_name = mhtml_store.make_save_file_name(test_weibo_data)
        await mhtml_store.save_data_to_mhtml(test_weibo_data)
        
        if os.path.exists(save_file_name):
            file_size = os.path.getsize(save_file_name)
            print(f"   ✅ 测试文件保存成功: {save_file_name}")
            print(f"   📁 文件大小: {file_size} bytes ({file_size/1024:.1f} KB)")
            
            # 验证文件内容
            with open(save_file_name, 'r', encoding='utf-8-sig') as f:
                saved_content = f.read()
                print(f"   📊 保存的文件验证:")
                print(f"      文件可读: {len(saved_content) > 0}")
                print(f"      编码正确: {'Edge测试用户' in saved_content or 'edge_test' in saved_content}")
                print(f"      格式完整: {'MIME-Version' in saved_content}")
        
        print(f"\n🎯 Edge兼容性修复总结:")
        print(f"   ✅ 添加了X-MimeOLE头部提高Edge识别")
        print(f"   ✅ 使用quoted-printable编码替代8bit")
        print(f"   ✅ 添加了Edge特定的CSS兼容性样式")
        print(f"   ✅ 移除了可能导致重复显示的元素")
        print(f"   ✅ 确保了正确的字符编码声明")
        print(f"   ✅ 修复了HTML结构和DOCTYPE声明")
        
        print(f"\n🌐 测试建议:")
        print(f"   1. 在Edge浏览器中打开生成的MHTML文件")
        print(f"   2. 验证是否能看到完整的微博内容而不是重复的按钮")
        print(f"   3. 检查中文字符是否正确显示")
        print(f"   4. 确认页面布局是否正常")
        
        print(f"\n📁 测试文件位置:")
        print(f"   {os.path.abspath(save_file_name)}")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


async def test_html_processing_details():
    """详细测试HTML处理逻辑"""
    
    print(f"\n" + "=" * 60)
    print("HTML处理详细测试")
    print("=" * 60)
    
    mhtml_store = WeiboMhtmlStoreImplement()
    
    # 模拟可能导致Edge显示问题的HTML
    problematic_html = """
<html>
<body>
    <div class="m-text-box">正常的微博内容</div>
    <div class="m-btn-box" onclick="like()">点赞</div>
    <div class="m-btn-box" onclick="comment()">评论</div>
    <div class="m-btn-box" onclick="repost()">转发</div>
    <script>
        function like() { console.log('like'); }
        function comment() { console.log('comment'); }
        function repost() { console.log('repost'); }
    </script>
    <div data-v-123="test">动态内容</div>
</body>
</html>
"""
    
    print(f"🔍 处理前的HTML:")
    print(f"   包含onclick事件: {'onclick=' in problematic_html}")
    print(f"   包含script标签: {'<script>' in problematic_html}")
    print(f"   包含data-v属性: {'data-v-' in problematic_html}")
    print(f"   包含按钮元素: {'m-btn-box' in problematic_html}")
    
    processed = mhtml_store.process_original_html(problematic_html, "test")
    
    print(f"\n🔧 处理后的HTML:")
    print(f"   移除了onclick事件: {'onclick=' not in processed}")
    print(f"   移除了script标签: {'<script>' not in processed}")
    print(f"   移除了data-v属性: {'data-v-' not in processed}")
    print(f"   添加了Edge样式: {'.m-btn-box' in processed and 'display: none' in processed}")
    print(f"   添加了DOCTYPE: {'<!DOCTYPE html>' in processed}")
    print(f"   添加了charset: {'charset=utf-8' in processed}")
    
    print(f"\n📝 处理后的HTML预览（前500字符）:")
    print("-" * 50)
    print(processed[:500] + "...")
    print("-" * 50)


if __name__ == "__main__":
    asyncio.run(test_edge_compatibility_fixes())
    asyncio.run(test_html_processing_details())
