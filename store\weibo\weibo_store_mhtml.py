# -*- coding: utf-8 -*-
"""
微博MHTML存储实现
"""

import os
import pathlib
import base64
import mimetypes
import re
from typing import Dict, Optional
from urllib.parse import urljoin, urlparse

import aiofiles
import httpx

import config
from base.base_crawler import AbstractStore
from tools import utils


class WeiboMhtmlStoreImplement(AbstractStore):
    """微博MHTML存储实现类"""
    
    mhtml_store_path: str = "data/weibo/mhtml"
    
    def __init__(self):
        """初始化MHTML存储"""
        pathlib.Path(self.mhtml_store_path).mkdir(parents=True, exist_ok=True)
    
    def sanitize_filename(self, filename: str) -> str:
        """清理文件名中的特殊字符"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename.strip()
    
    def make_save_file_name(self, save_item: Dict) -> str:
        """
        根据用户信息和帖子ID生成MHTML文件路径
        Args:
            save_item: 包含用户信息和帖子信息的字典
        Returns: eg: data/weibo/mhtml/用户名_用户ID/帖子ID.mhtml
        """
        note_id = save_item.get("note_id", "unknown")
        nickname = save_item.get("nickname", "未知用户")
        user_id = save_item.get("user_id", "unknown")
        
        # 清理用户名中的特殊字符
        clean_nickname = self.sanitize_filename(nickname)
        
        # 构建用户文件夹路径
        user_folder = f"{clean_nickname}_{user_id}"
        user_path = f"{self.mhtml_store_path}/{user_folder}"
        
        # 确保用户文件夹存在
        pathlib.Path(user_path).mkdir(parents=True, exist_ok=True)
        
        # 返回完整文件路径
        return f"{user_path}/{note_id}.mhtml"
    
    async def get_image_base64(self, image_path: str) -> str:
        """
        获取图片的base64编码
        Args:
            image_path: 图片文件路径
        Returns: base64编码的图片数据
        """
        try:
            if os.path.exists(image_path):
                async with aiofiles.open(image_path, 'rb') as f:
                    image_data = await f.read()
                    return base64.b64encode(image_data).decode('utf-8')
            else:
                utils.logger.warning(f"[WeiboMhtmlStoreImplement.get_image_base64] Image not found: {image_path}")
                return ""
        except Exception as e:
            utils.logger.error(f"[WeiboMhtmlStoreImplement.get_image_base64] Error reading image {image_path}: {e}")
            return ""
    
    def get_mime_type(self, file_path: str) -> str:
        """
        获取文件的MIME类型
        Args:
            file_path: 文件路径
        Returns: MIME类型
        """
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or "application/octet-stream"

    async def fetch_original_weibo_html(self, note_id: str) -> Optional[str]:
        """
        获取微博原始HTML内容
        Args:
            note_id: 微博ID
        Returns: 原始HTML内容
        """
        try:
            # 构建微博URL
            weibo_url = f"https://m.weibo.cn/detail/{note_id}"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            async with httpx.AsyncClient(timeout=30) as client:
                response = await client.get(weibo_url, headers=headers)

                if response.status_code == 200:
                    html_content = response.text
                    utils.logger.info(f"[WeiboMhtmlStoreImplement.fetch_original_weibo_html] Successfully fetched HTML for note {note_id}")
                    return html_content
                else:
                    utils.logger.warning(f"[WeiboMhtmlStoreImplement.fetch_original_weibo_html] Failed to fetch HTML for note {note_id}, status: {response.status_code}")
                    return None

        except Exception as e:
            utils.logger.error(f"[WeiboMhtmlStoreImplement.fetch_original_weibo_html] Error fetching HTML for note {note_id}: {e}")
            return None

    async def fetch_css_content(self, css_url: str, base_url: str) -> Optional[str]:
        """
        获取CSS文件内容
        Args:
            css_url: CSS文件URL
            base_url: 基础URL，用于解析相对路径
        Returns: CSS内容
        """
        try:
            # 处理相对URL
            if css_url.startswith('//'):
                css_url = 'https:' + css_url
            elif css_url.startswith('/'):
                css_url = urljoin(base_url, css_url)
            elif not css_url.startswith('http'):
                css_url = urljoin(base_url, css_url)

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/css,*/*;q=0.1',
                'Referer': base_url,
            }

            async with httpx.AsyncClient(timeout=15) as client:
                response = await client.get(css_url, headers=headers)

                if response.status_code == 200:
                    return response.text
                else:
                    utils.logger.warning(f"[WeiboMhtmlStoreImplement.fetch_css_content] Failed to fetch CSS: {css_url}")
                    return None

        except Exception as e:
            utils.logger.error(f"[WeiboMhtmlStoreImplement.fetch_css_content] Error fetching CSS {css_url}: {e}")
            return None

    def process_original_html(self, html_content: str, note_id: str) -> str:
        """
        处理原始HTML内容，移除不必要的脚本和元素
        Args:
            html_content: 原始HTML内容
            note_id: 微博ID
        Returns: 处理后的HTML内容
        """
        try:
            # 移除script标签（保留样式相关的）
            html_content = re.sub(r'<script(?![^>]*type=["\']text/css["\'])[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

            # 移除一些可能导致问题的元素
            html_content = re.sub(r'<iframe[^>]*>.*?</iframe>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<object[^>]*>.*?</object>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<embed[^>]*>', '', html_content, flags=re.IGNORECASE)

            # 移除一些可能的跟踪和广告相关的元素
            html_content = re.sub(r'<[^>]*(?:data-track|data-analytics|data-ad)[^>]*>', '', html_content, flags=re.IGNORECASE)

            # 添加一个标识，表明这是保存的版本
            html_content = html_content.replace(
                '<head>',
                f'<head>\n<meta name="saved-by" content="MediaCrawler">\n<meta name="original-note-id" content="{note_id}">'
            )

            return html_content

        except Exception as e:
            utils.logger.error(f"[WeiboMhtmlStoreImplement.process_original_html] Error processing HTML: {e}")
            return html_content
    
    async def generate_mhtml_content(self, save_item: Dict) -> str:
        """
        生成MHTML内容，优先使用原始HTML
        Args:
            save_item: 微博数据项
        Returns: MHTML格式的内容
        """
        note_id = save_item.get("note_id", "unknown")
        nickname = save_item.get("nickname", "未知用户")
        user_id = save_item.get("user_id", "unknown")
        content = save_item.get("content", "")
        created_at = save_item.get("created_at", "")

        # MHTML边界标识符
        boundary = f"----=_NextPart_{note_id}"

        # 根据配置决定是否使用原始HTML
        if config.MHTML_USE_ORIGINAL_STYLE:
            # 尝试获取原始HTML内容
            utils.logger.info(f"[WeiboMhtmlStoreImplement.generate_mhtml_content] Attempting to fetch original HTML for note {note_id}")
            original_html = await self.fetch_original_weibo_html(note_id)

            if original_html:
                # 使用原始HTML内容
                utils.logger.info(f"[WeiboMhtmlStoreImplement.generate_mhtml_content] Using original HTML for note {note_id}")
                return await self.generate_mhtml_from_original_html(original_html, save_item, boundary)
            else:
                # 回退到自定义HTML模板
                utils.logger.info(f"[WeiboMhtmlStoreImplement.generate_mhtml_content] Falling back to custom template for note {note_id}")
                return await self.generate_mhtml_from_template(save_item, boundary)
        else:
            # 直接使用自定义模板
            utils.logger.info(f"[WeiboMhtmlStoreImplement.generate_mhtml_content] Using custom template for note {note_id} (original style disabled)")
            return await self.generate_mhtml_from_template(save_item, boundary)

    async def generate_mhtml_from_original_html(self, original_html: str, save_item: Dict, boundary: str) -> str:
        """
        从原始HTML生成MHTML内容
        Args:
            original_html: 原始HTML内容
            save_item: 微博数据项
            boundary: MHTML边界标识符
        Returns: MHTML格式的内容
        """
        note_id = save_item.get("note_id", "unknown")
        nickname = save_item.get("nickname", "未知用户")

        # 处理HTML内容
        processed_html = self.process_original_html(original_html, note_id)

        # 开始构建MHTML内容
        mhtml_parts = []

        # MHTML头部
        mhtml_parts.append(f"""From: <Saved by MediaCrawler>
Subject: Weibo - {nickname} - {note_id}
Date: {utils.get_current_date()}
MIME-Version: 1.0
Content-Type: multipart/related;
\tboundary="{boundary}";
\ttype="text/html"

--{boundary}
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: 8bit
Content-Location: weibo_{note_id}.html

{processed_html}""")

        # 提取并嵌入CSS文件
        await self.embed_css_resources(processed_html, mhtml_parts, boundary, f"https://m.weibo.cn/detail/{note_id}")

        # 嵌入本地图片资源
        await self.embed_local_images(save_item, mhtml_parts, boundary)

        # MHTML结束标记
        mhtml_parts.append(f"\n--{boundary}--")

        return "\n".join(mhtml_parts)

    async def embed_css_resources(self, html_content: str, mhtml_parts: list, boundary: str, base_url: str):
        """
        提取并嵌入CSS资源
        Args:
            html_content: HTML内容
            mhtml_parts: MHTML部分列表
            boundary: 边界标识符
            base_url: 基础URL
        """
        try:
            # 查找CSS链接
            css_links = re.findall(r'<link[^>]*rel=["\']stylesheet["\'][^>]*href=["\']([^"\']+)["\'][^>]*>', html_content, re.IGNORECASE)

            for i, css_url in enumerate(css_links):
                css_content = await self.fetch_css_content(css_url, base_url)
                if css_content:
                    # 添加CSS到MHTML
                    css_filename = f"style_{i}.css"
                    mhtml_parts.append(f"""
--{boundary}
Content-Type: text/css
Content-Transfer-Encoding: 8bit
Content-Location: {css_filename}

{css_content}""")

                    utils.logger.info(f"[WeiboMhtmlStoreImplement.embed_css_resources] Embedded CSS: {css_url}")

        except Exception as e:
            utils.logger.error(f"[WeiboMhtmlStoreImplement.embed_css_resources] Error embedding CSS: {e}")

    async def embed_local_images(self, save_item: Dict, mhtml_parts: list, boundary: str):
        """
        嵌入本地图片资源
        Args:
            save_item: 微博数据项
            mhtml_parts: MHTML部分列表
            boundary: 边界标识符
        """
        try:
            local_image_paths = save_item.get("local_image_paths", [])

            if local_image_paths:
                utils.logger.info(f"[WeiboMhtmlStoreImplement.embed_local_images] Embedding {len(local_image_paths)} local images")

                for image_path in local_image_paths:
                    if os.path.exists(image_path):
                        # 获取图片的base64编码
                        image_base64 = await self.get_image_base64(image_path)
                        if image_base64:
                            # 获取文件名和MIME类型
                            filename = os.path.basename(image_path)
                            mime_type = self.get_mime_type(image_path)

                            # 添加图片到MHTML
                            mhtml_parts.append(f"""
--{boundary}
Content-Type: {mime_type}
Content-Transfer-Encoding: base64
Content-Location: images/{filename}

{image_base64}""")

                            utils.logger.info(f"[WeiboMhtmlStoreImplement.embed_local_images] Embedded image: {filename}")

        except Exception as e:
            utils.logger.error(f"[WeiboMhtmlStoreImplement.embed_local_images] Error embedding images: {e}")
        
    async def generate_mhtml_from_template(self, save_item: Dict, boundary: str) -> str:
        """
        从自定义模板生成MHTML内容（回退方案）
        Args:
            save_item: 微博数据项
            boundary: MHTML边界标识符
        Returns: MHTML格式的内容
        """
        note_id = save_item.get("note_id", "unknown")
        nickname = save_item.get("nickname", "未知用户")
        user_id = save_item.get("user_id", "unknown")
        content = save_item.get("content", "")
        created_at = save_item.get("created_at", "")

        # 构建HTML内容，使用HTML实体编码避免中文字符问题
        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Weibo - {nickname} - {note_id}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .weibo-container {{
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }}
        .user-info {{
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }}
        .user-info h3 {{
            margin: 0;
            color: #1890ff;
            font-size: 16px;
        }}
        .user-info p {{
            margin: 5px 0 0 0;
            color: #666;
            font-size: 14px;
        }}
        .content {{
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 20px;
            white-space: pre-wrap;
        }}
        .images {{
            margin: 20px 0;
        }}
        .images img {{
            max-width: 100%;
            height: auto;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        .metadata {{
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
            padding-top: 15px;
            margin-top: 20px;
        }}
        .metadata p {{
            margin: 5px 0;
        }}
    </style>
</head>
<body>
    <div class="weibo-container">
        <div class="user-info">
            <h3>{nickname}</h3>
            <p>User ID: {user_id}</p>
            <p>Published: {created_at}</p>
        </div>

        <div class="content">{content}</div>

        <div class="images" id="images-container">
            <!-- Images will be inserted here -->
        </div>

        <div class="metadata">
            <p><strong>Weibo ID:</strong> {note_id}</p>
            <p><strong>Likes:</strong> {save_item.get('attitudes_count', 0)}</p>
            <p><strong>Comments:</strong> {save_item.get('comments_count', 0)}</p>
            <p><strong>Reposts:</strong> {save_item.get('reposts_count', 0)}</p>
            <p><strong>Saved:</strong> {utils.get_current_date()}</p>
        </div>
    </div>
</body>
</html>"""
        
        # 开始构建MHTML内容
        mhtml_parts = []
        
        # MHTML头部
        mhtml_parts.append(f"""From: <Saved by MediaCrawler>
Subject: Weibo - {nickname} - {note_id}
Date: {utils.get_current_date()}
MIME-Version: 1.0
Content-Type: multipart/related;
\tboundary="{boundary}";
\ttype="text/html"

--{boundary}
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: 8bit
Content-Location: weibo_{note_id}.html

{html_content}""")
        
        # 嵌入本地图片资源
        await self.embed_local_images(save_item, mhtml_parts, boundary)

        # 处理图片显示（在HTML中插入图片标签）
        local_image_paths = save_item.get("local_image_paths", [])
        if local_image_paths:
            images_html = ""
            for i, image_path in enumerate(local_image_paths):
                if os.path.exists(image_path):
                    filename = os.path.basename(image_path)
                    images_html += f'<img src="images/{filename}" alt="Image {i+1}" />\n'

            # 将图片插入到HTML中
            if images_html:
                html_with_images = html_content.replace(
                    '<!-- Images will be inserted here -->',
                    images_html
                )
                # 更新HTML部分
                mhtml_parts[0] = mhtml_parts[0].replace(html_content, html_with_images)

        # MHTML结束标记
        mhtml_parts.append(f"\n--{boundary}--")

        return "\n".join(mhtml_parts)
    
    async def save_data_to_mhtml(self, save_item: Dict):
        """
        将数据保存为MHTML格式
        Args:
            save_item: 微博数据项
        """
        if not config.ENABLE_GET_MEDIAS:
            utils.logger.info(f"[WeiboMhtmlStoreImplement.save_data_to_mhtml] MHTML saving is disabled (ENABLE_GET_MEDIAS=False)")
            return
        
        try:
            save_file_name = self.make_save_file_name(save_item)
            note_id = save_item.get("note_id", "unknown")
            
            utils.logger.info(f"[WeiboMhtmlStoreImplement.save_data_to_mhtml] Saving note {note_id} to MHTML: {save_file_name}")
            
            # 生成MHTML内容
            mhtml_content = await self.generate_mhtml_content(save_item)
            
            # 保存到文件，使用utf-8-sig编码确保BOM标记
            async with aiofiles.open(save_file_name, 'w', encoding='utf-8-sig') as f:
                await f.write(mhtml_content)
            
            utils.logger.info(f"[WeiboMhtmlStoreImplement.save_data_to_mhtml] Successfully saved MHTML: {save_file_name}")
            
        except Exception as e:
            utils.logger.error(f"[WeiboMhtmlStoreImplement.save_data_to_mhtml] Error saving MHTML: {e}")
    
    async def store_content(self, content_item: Dict):
        """
        微博内容MHTML存储实现
        Args:
            content_item: 微博内容项
        """
        await self.save_data_to_mhtml(save_item=content_item)
    
    async def store_comment(self, comment_item: Dict):
        """
        微博评论存储实现（MHTML格式不单独存储评论）
        Args:
            comment_item: 评论项
        """
        # MHTML格式通常不单独存储评论，评论会包含在主内容中
        pass
    
    async def store_creator(self, creator_item: Dict):
        """
        创作者信息存储实现（MHTML格式不单独存储创作者信息）
        Args:
            creator_item: 创作者信息项
        """
        # MHTML格式通常不单独存储创作者信息
        pass
