# -*- coding: utf-8 -*-
"""
微博MHTML存储实现
"""

import os
import pathlib
import base64
import mimetypes
from typing import Dict

import aiofiles

import config
from base.base_crawler import AbstractStore
from tools import utils


class WeiboMhtmlStoreImplement(AbstractStore):
    """微博MHTML存储实现类"""
    
    mhtml_store_path: str = "data/weibo/mhtml"
    
    def __init__(self):
        """初始化MHTML存储"""
        pathlib.Path(self.mhtml_store_path).mkdir(parents=True, exist_ok=True)
    
    def sanitize_filename(self, filename: str) -> str:
        """清理文件名中的特殊字符"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename.strip()
    
    def make_save_file_name(self, save_item: Dict) -> str:
        """
        根据用户信息和帖子ID生成MHTML文件路径
        Args:
            save_item: 包含用户信息和帖子信息的字典
        Returns: eg: data/weibo/mhtml/用户名_用户ID/帖子ID.mhtml
        """
        note_id = save_item.get("note_id", "unknown")
        nickname = save_item.get("nickname", "未知用户")
        user_id = save_item.get("user_id", "unknown")
        
        # 清理用户名中的特殊字符
        clean_nickname = self.sanitize_filename(nickname)
        
        # 构建用户文件夹路径
        user_folder = f"{clean_nickname}_{user_id}"
        user_path = f"{self.mhtml_store_path}/{user_folder}"
        
        # 确保用户文件夹存在
        pathlib.Path(user_path).mkdir(parents=True, exist_ok=True)
        
        # 返回完整文件路径
        return f"{user_path}/{note_id}.mhtml"
    
    async def get_image_base64(self, image_path: str) -> str:
        """
        获取图片的base64编码
        Args:
            image_path: 图片文件路径
        Returns: base64编码的图片数据
        """
        try:
            if os.path.exists(image_path):
                async with aiofiles.open(image_path, 'rb') as f:
                    image_data = await f.read()
                    return base64.b64encode(image_data).decode('utf-8')
            else:
                utils.logger.warning(f"[WeiboMhtmlStoreImplement.get_image_base64] Image not found: {image_path}")
                return ""
        except Exception as e:
            utils.logger.error(f"[WeiboMhtmlStoreImplement.get_image_base64] Error reading image {image_path}: {e}")
            return ""
    
    def get_mime_type(self, file_path: str) -> str:
        """
        获取文件的MIME类型
        Args:
            file_path: 文件路径
        Returns: MIME类型
        """
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or "application/octet-stream"
    
    async def generate_mhtml_content(self, save_item: Dict) -> str:
        """
        生成MHTML内容
        Args:
            save_item: 微博数据项
        Returns: MHTML格式的内容
        """
        note_id = save_item.get("note_id", "unknown")
        nickname = save_item.get("nickname", "未知用户")
        user_id = save_item.get("user_id", "unknown")
        content = save_item.get("content", "")
        created_at = save_item.get("created_at", "")
        
        # MHTML边界标识符
        boundary = f"----=_NextPart_{note_id}"
        
        # 构建HTML内容，使用HTML实体编码避免中文字符问题
        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Weibo - {nickname} - {note_id}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .weibo-container {{
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }}
        .user-info {{
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }}
        .user-info h3 {{
            margin: 0;
            color: #1890ff;
            font-size: 16px;
        }}
        .user-info p {{
            margin: 5px 0 0 0;
            color: #666;
            font-size: 14px;
        }}
        .content {{
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 20px;
            white-space: pre-wrap;
        }}
        .images {{
            margin: 20px 0;
        }}
        .images img {{
            max-width: 100%;
            height: auto;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        .metadata {{
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
            padding-top: 15px;
            margin-top: 20px;
        }}
        .metadata p {{
            margin: 5px 0;
        }}
    </style>
</head>
<body>
    <div class="weibo-container">
        <div class="user-info">
            <h3>{nickname}</h3>
            <p>User ID: {user_id}</p>
            <p>Published: {created_at}</p>
        </div>

        <div class="content">{content}</div>

        <div class="images" id="images-container">
            <!-- Images will be inserted here -->
        </div>

        <div class="metadata">
            <p><strong>Weibo ID:</strong> {note_id}</p>
            <p><strong>Likes:</strong> {save_item.get('attitudes_count', 0)}</p>
            <p><strong>Comments:</strong> {save_item.get('comments_count', 0)}</p>
            <p><strong>Reposts:</strong> {save_item.get('reposts_count', 0)}</p>
            <p><strong>Saved:</strong> {utils.get_current_date()}</p>
        </div>
    </div>
</body>
</html>"""
        
        # 开始构建MHTML内容
        mhtml_parts = []
        
        # MHTML头部
        mhtml_parts.append(f"""From: <Saved by MediaCrawler>
Subject: 微博 - {nickname} - {note_id}
Date: {utils.get_current_date()}
MIME-Version: 1.0
Content-Type: multipart/related;
\tboundary="{boundary}";
\ttype="text/html"

--{boundary}
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: 8bit
Content-Location: weibo_{note_id}.html

{html_content}""")
        
        # 处理图片
        local_image_paths = save_item.get("local_image_paths", [])
        
        if local_image_paths:
            utils.logger.info(f"[WeiboMhtmlStoreImplement.generate_mhtml_content] Processing {len(local_image_paths)} images for note {note_id}")
            
            images_html = ""
            for i, image_path in enumerate(local_image_paths):
                if os.path.exists(image_path):
                    # 获取图片的base64编码
                    image_base64 = await self.get_image_base64(image_path)
                    if image_base64:
                        # 获取文件名和MIME类型
                        filename = os.path.basename(image_path)
                        mime_type = self.get_mime_type(image_path)
                        
                        # 添加图片到MHTML
                        mhtml_parts.append(f"""
--{boundary}
Content-Type: {mime_type}
Content-Transfer-Encoding: base64
Content-Location: images/{filename}

{image_base64}""")
                        
                        # 添加图片到HTML
                        images_html += f'<img src="images/{filename}" alt="Image {i+1}" />\n'
            
            # 将图片插入到HTML中
            if images_html:
                html_with_images = html_content.replace(
                    '<!-- Images will be inserted here -->',
                    images_html
                )
                # 更新HTML部分
                mhtml_parts[0] = mhtml_parts[0].replace(html_content, html_with_images)
        
        # MHTML结束标记
        mhtml_parts.append(f"\n--{boundary}--")
        
        return "\n".join(mhtml_parts)
    
    async def save_data_to_mhtml(self, save_item: Dict):
        """
        将数据保存为MHTML格式
        Args:
            save_item: 微博数据项
        """
        if not config.ENABLE_GET_MEDIAS:
            utils.logger.info(f"[WeiboMhtmlStoreImplement.save_data_to_mhtml] MHTML saving is disabled (ENABLE_GET_MEDIAS=False)")
            return
        
        try:
            save_file_name = self.make_save_file_name(save_item)
            note_id = save_item.get("note_id", "unknown")
            
            utils.logger.info(f"[WeiboMhtmlStoreImplement.save_data_to_mhtml] Saving note {note_id} to MHTML: {save_file_name}")
            
            # 生成MHTML内容
            mhtml_content = await self.generate_mhtml_content(save_item)
            
            # 保存到文件，使用utf-8-sig编码确保BOM标记
            async with aiofiles.open(save_file_name, 'w', encoding='utf-8-sig') as f:
                await f.write(mhtml_content)
            
            utils.logger.info(f"[WeiboMhtmlStoreImplement.save_data_to_mhtml] Successfully saved MHTML: {save_file_name}")
            
        except Exception as e:
            utils.logger.error(f"[WeiboMhtmlStoreImplement.save_data_to_mhtml] Error saving MHTML: {e}")
    
    async def store_content(self, content_item: Dict):
        """
        微博内容MHTML存储实现
        Args:
            content_item: 微博内容项
        """
        await self.save_data_to_mhtml(save_item=content_item)
    
    async def store_comment(self, comment_item: Dict):
        """
        微博评论存储实现（MHTML格式不单独存储评论）
        Args:
            comment_item: 评论项
        """
        # MHTML格式通常不单独存储评论，评论会包含在主内容中
        pass
    
    async def store_creator(self, creator_item: Dict):
        """
        创作者信息存储实现（MHTML格式不单独存储创作者信息）
        Args:
            creator_item: 创作者信息项
        """
        # MHTML格式通常不单独存储创作者信息
        pass
