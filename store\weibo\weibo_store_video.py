# -*- coding: utf-8 -*-
# <AUTHOR> <EMAIL>
# @Time    : 2024/1/14 17:05
# @Desc    : 微博视频存储实现

import os
import pathlib
from typing import Dict

import aiofiles

from base.base_crawler import AbstractStoreVideo
from tools import utils


class WeiboStoreVideo(AbstractStoreVideo):
    video_store_path: str = "data/weibo/videos"

    async def store_video(self, video_content_item: Dict):
        """
        store video content
        
        Args:
            video_content_item: 视频内容项，包含video_id、video_content、extension_file_name

        Returns:

        """
        await self.save_video(
            video_content_item.get("video_id"), 
            video_content_item.get("video_content"), 
            video_content_item.get("extension_file_name")
        )

    def sanitize_filename(self, filename: str) -> str:
        """
        清理文件名中的特殊字符
        Args:
            filename: 原始文件名

        Returns: 清理后的文件名
        """
        # 替换特殊字符为下划线
        import re
        return re.sub(r'[<>:"/\\|?*]', '_', filename)

    def make_save_file_name(self, video_id: str, extension_file_name: str) -> str:
        """
        make save file name by video id

        Args:
            video_id: 视频ID
            extension_file_name: 视频文件扩展名

        Returns:
            完整的文件保存路径
        """
        # 清理视频ID中的特殊字符
        clean_video_id = self.sanitize_filename(video_id)
        return f"{self.video_store_path}/{clean_video_id}.{extension_file_name}"

    async def save_video(self, video_id: str, video_content: bytes, extension_file_name: str = "mp4"):
        """
        save video to local
        
        Args:
            video_id: 视频ID
            video_content: 视频内容
            extension_file_name: 视频文件扩展名

        Returns:

        """
        pathlib.Path(self.video_store_path).mkdir(parents=True, exist_ok=True)
        save_file_name = self.make_save_file_name(video_id, extension_file_name)
        async with aiofiles.open(save_file_name, 'wb') as f:
            await f.write(video_content)
            utils.logger.info(f"[WeiboStoreVideo.save_video] save video {save_file_name} success ...")
