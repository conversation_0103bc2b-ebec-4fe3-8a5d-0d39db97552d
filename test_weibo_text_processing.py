#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微博文本图片处理功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from store.weibo import _process_weibo_text_with_images


def test_weibo_text_processing():
    """测试微博文本图片处理功能"""
    
    print("=" * 60)
    print("微博文本图片处理测试")
    print("=" * 60)
    
    # 测试数据：来自实际微博的HTML内容
    test_html = """【衡阳之战的几点问题】1、曾任国府国防部作战厅长的郭汝瑰将军回忆录：<br /><br />"原暂五师的柏松林参谋，1944年在方先觉部任情报科长。柏告诉我，投降是方先觉叫副官处长出去联络的，投降时部队撤下来还有一万多人，机枪迫击炮等尚多，方先觉一见就哭起来了。对柏说：'早晓得还有这么多枪和人，我就不投降了。'"👉<a  href="https://weibo.cn/sinaurl?u=https%3A%2F%2Fwx1.sinaimg.cn%2Flarge%2F6d81facegy1hq0uz3fiddj20u00yhtd2.jpg" data-hide=""><span class='url-icon'><img style='width: 1rem;height: 1rem' src='https://h5.sinaimg.cn/upload/2015/01/21/20/timeline_card_small_photo_default.png'></span><span class="surl-text">评论配图</span></a><br /><br />2、第十军军长方先觉率四个师长向日军集体投降。方先觉投降后，仍有官兵在继续抵抗日军：<a  href="https://weibo.cn/sinaurl?u=https%3A%2F%2Fwx4.sinaimg.cn%2Flarge%2F6d81facegy1hq0vcrgsb2j20jc04ijs2.jpg" data-hide=""><span class='url-icon'><img style='width: 1rem;height: 1rem' src='https://h5.sinaimg.cn/upload/2015/01/21/20/timeline_card_small_photo_default.png'></span><span class="surl-text">评论配图</span></a><br /><br />3、军令部长徐永昌上将1944年8月15日日记："据逃出之梁团长子超（第10军190师569团长）在连络站电话，围攻衡阳之敌于七日突破三个缺口窜入城内，方军长派其副官处长向敌提出六项要求与敌接洽。<br /><br />其要旨如次：1.不解除武装，不分割建制；2.指定地点集中训练；3.受伤害官兵不得杀害；【4.送往南京；】5.保障生命安全；6.眷属送安全地点。<br /><br />而结果被敌所骗，均未接受。将副师长以下干部充工头，扫除街道。遇我飞机轰炸逃亡甚多。" <a  href="https://weibo.cn/sinaurl?u=https%3A%2F%2Fwx1.sinaimg.cn%2Flarge%2F6d81facegy1hq0vl5gj12j20qo0esn04.jpg" data-hide=""><span class='url-icon'><img style='width: 1rem;height: 1rem' src='https://h5.sinaimg.cn/upload/2015/01/21/20/timeline_card_small_photo_default.png'></span><span class="surl-text">评论配图</span></a>"""
    
    print("🔍 原始HTML内容（前200字符）:")
    print(test_html[:200] + "...")
    
    print(f"\n📸 原始内容中的图片链接:")
    import re
    import urllib.parse
    
    # 查找所有图片链接
    pattern = r'<a[^>]*href="[^"]*https%3A%2F%2Fwx\d+\.sinaimg\.cn%2Flarge%2F([^"\'>\s&]+)"[^>]*>.*?<span[^>]*>([^<]*)</span></a>'
    matches = re.finditer(pattern, test_html)
    
    found_images = []
    for i, match in enumerate(matches, 1):
        image_id = urllib.parse.unquote(match.group(1))
        link_text = match.group(2)
        print(f"   {i}. 图片ID: {image_id}")
        print(f"      链接文本: {link_text}")
        found_images.append((image_id, link_text))
    
    print(f"\n🧪 测试文本处理功能:")
    
    try:
        # 调用文本处理函数
        processed_text = _process_weibo_text_with_images(test_html)
        
        print(f"✅ 文本处理成功!")
        print(f"\n📝 处理后的文本内容:")
        print("-" * 40)
        print(processed_text)
        print("-" * 40)
        
        # 检查是否包含Markdown图片链接
        markdown_pattern = r'!\[图片\d+\]\([^)]+\)'
        markdown_matches = re.findall(markdown_pattern, processed_text)
        
        print(f"\n📊 处理结果分析:")
        print(f"   原始图片链接数量: {len(found_images)}")
        print(f"   生成的Markdown图片数量: {len(markdown_matches)}")
        
        if markdown_matches:
            print(f"   生成的Markdown图片链接:")
            for i, md_link in enumerate(markdown_matches, 1):
                print(f"      {i}. {md_link}")
        
        # 检查是否还有"评论配图"文本
        if "评论配图" in processed_text:
            print(f"   ⚠️  警告: 文本中仍包含'评论配图'，可能未完全替换")
        else:
            print(f"   ✅ 所有'评论配图'文本已被替换为Markdown图片链接")
        
        # 验证图片文件是否存在
        print(f"\n📁 验证本地图片文件:")
        for image_id, link_text in found_images:
            pic_filename = image_id.split('.')[0] if '.' in image_id else image_id
            extension = image_id.split('.')[-1] if '.' in image_id else "jpg"
            image_path = f"data/weibo/images/{pic_filename}.{extension}"
            
            if os.path.exists(image_path):
                file_size = os.path.getsize(image_path)
                print(f"   ✅ {pic_filename}.{extension} - 存在 ({file_size} bytes)")
            else:
                print(f"   ❌ {pic_filename}.{extension} - 不存在")
        
        print(f"\n🎯 修复效果总结:")
        if len(markdown_matches) == len(found_images) and "评论配图" not in processed_text:
            print(f"   ✅ 修复成功！所有图片链接已正确转换为Markdown格式")
        else:
            print(f"   ⚠️  修复部分成功，可能需要进一步调整")
            
    except Exception as e:
        print(f"❌ 文本处理失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_weibo_text_processing()
