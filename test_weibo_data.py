#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微博数据结构获取脚本
用于分析微博图片爬取问题
"""

import asyncio
import json
import sys
import os
import re

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import httpx
import config


async def test_weibo_data():
    """测试获取微博数据结构"""

    # 测试微博ID
    test_note_id = "Q1ufxC7ce"  # 来自用户提供的示例链接

    print(f"正在获取微博 {test_note_id} 的数据结构...")

    # 创建简单的HTTP客户端
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    try:
        url = f"https://m.weibo.cn/detail/{test_note_id}"

        async with httpx.AsyncClient() as client:
            response = await client.request("GET", url, timeout=30, headers=headers)

            if response.status_code != 200:
                print(f"HTTP请求失败: {response.status_code}")
                return

            # 解析页面中的render_data
            match = re.search(r'var \$render_data = (\[.*?\])\[0\]', response.text, re.DOTALL)
            if match:
                render_data_json = match.group(1)
                render_data_dict = json.loads(render_data_json)
                note_detail = render_data_dict[0].get("status")

                if note_detail:
                    print("=" * 50)
                    print("微博数据结构:")
                    print("=" * 50)

                    # 美化输出JSON（只输出关键部分）
                    print(json.dumps(note_detail, indent=2, ensure_ascii=False)[:2000] + "...")

                    print("\n" + "=" * 50)
                    print("图片相关字段分析:")
                    print("=" * 50)

                    # 检查pics字段
                    pics = note_detail.get("pics")
                    print(f"pics字段: {pics}")

                    # 检查pic_infos字段
                    pic_infos = note_detail.get("pic_infos")
                    print(f"pic_infos字段: {pic_infos}")

                    # 检查pic_ids字段
                    pic_ids = note_detail.get("pic_ids")
                    print(f"pic_ids字段: {pic_ids}")

                    # 检查其他可能的图片字段
                    print("\n所有包含'pic'、'image'、'img'的字段:")
                    for key in note_detail.keys():
                        if 'pic' in key.lower() or 'image' in key.lower() or 'img' in key.lower():
                            value = note_detail.get(key)
                            if isinstance(value, (list, dict)) and value:
                                print(f"{key}字段: {type(value)} - {len(value) if isinstance(value, (list, dict)) else 'N/A'} 项")
                                if isinstance(value, list) and len(value) > 0:
                                    print(f"  第一项示例: {value[0]}")
                                elif isinstance(value, dict):
                                    print(f"  字典键: {list(value.keys())[:5]}")
                            else:
                                print(f"{key}字段: {value}")

                    print("\n" + "=" * 50)
                    print("完整字段列表:")
                    print("=" * 50)
                    for key in sorted(note_detail.keys()):
                        print(f"- {key}")

                    # 检查配置
                    print("\n" + "=" * 50)
                    print("当前配置检查:")
                    print("=" * 50)
                    print(f"ENABLE_GET_MEIDAS: {getattr(config, 'ENABLE_GET_MEIDAS', 'NOT_FOUND')}")
                    print(f"ENABLE_GET_MEDIAS: {getattr(config, 'ENABLE_GET_MEDIAS', 'NOT_FOUND')}")

                else:
                    print("未找到status字段")
            else:
                print("未找到$render_data")

    except Exception as e:
        print(f"获取微博数据时出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_weibo_data())
